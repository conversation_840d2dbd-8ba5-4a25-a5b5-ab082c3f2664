/* Unified Navigation Menu */
.unified-navigation {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--theme-accent-light);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
}

.nav-brand {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--theme-primary);
  cursor: pointer;
  transition: color 0.3s ease;
}

.nav-brand:hover {
  color: var(--theme-accent);
}

.nav-brand mat-icon {
  font-size: 28px;
  width: 28px;
  height: 28px;
}

.nav-links {
  display: flex;
  align-items: center;
  gap: 8px;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 500;
  color: var(--theme-text);
  transition: all 0.3s ease;
  border-radius: 8px;
  padding: 8px 16px;
  position: relative;
}

.nav-link:hover {
  background-color: var(--theme-accent-light);
  color: var(--theme-primary);
}

/* Active state for navigation links */
.nav-link.active {
  background-color: var(--theme-primary);
  color: white;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(103, 69, 92, 0.3);
}

.nav-link.active:hover {
  background-color: var(--theme-accent);
  color: white;
}

/* Active indicator line */
.nav-link.active::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  height: 2px;
  background: linear-gradient(90deg, var(--theme-accent), var(--theme-primary));
  border-radius: 1px;
}

.nav-link mat-icon {
  font-size: 20px;
  width: 20px;
  height: 20px;
}

.nav-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.login-btn, .register-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 500;
  border-radius: 8px;
  padding: 8px 16px;
}

.register-btn {
  background: linear-gradient(135deg, var(--theme-primary), var(--theme-accent));
  color: white;
}

.nav-user-menu {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-info {
  font-size: 0.9rem;
  color: var(--theme-text-secondary);
  font-weight: 500;
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
  display: none;
  color: var(--theme-primary);
}

.mobile-menu-toggle.active {
  color: var(--theme-accent);
}

/* Mobile Menu */
.mobile-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(15px);
  border-bottom: 1px solid var(--theme-accent-light);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transform: translateY(-100%);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.mobile-menu.open {
  transform: translateY(0);
  opacity: 1;
  visibility: visible;
}

.mobile-nav-link {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border-radius: 8px;
  font-weight: 500;
  color: var(--theme-text);
  transition: all 0.3s ease;
  justify-content: flex-start;
  width: 100%;
  position: relative;
}

.mobile-nav-link:hover {
  background-color: var(--theme-accent-light);
  color: var(--theme-primary);
}

/* Active state for mobile navigation links */
.mobile-nav-link.active {
  background-color: var(--theme-primary);
  color: white;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(103, 69, 92, 0.3);
}

.mobile-nav-link.active:hover {
  background-color: var(--theme-accent);
  color: white;
}

/* Active indicator for mobile */
.mobile-nav-link.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 60%;
  background: linear-gradient(180deg, var(--theme-accent), white);
  border-radius: 0 2px 2px 0;
}

.mobile-actions, .mobile-user-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid var(--theme-accent-light);
}

.mobile-login-btn, .mobile-register-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 500;
  width: 100%;
}

.mobile-register-btn {
  background: linear-gradient(135deg, var(--theme-primary), var(--theme-accent));
  color: white;
}

/* User Menu Styling */
.user-menu {
  margin-top: 8px;
}

.user-menu .mat-menu-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  font-weight: 500;
}

.submenu-arrow {
  margin-left: auto;
  font-size: 18px;
}

.theme-menu-content {
  padding: 16px;
  min-width: 200px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-links,
  .nav-actions,
  .nav-user-menu .user-info {
    display: none;
  }

  .mobile-menu-toggle {
    display: flex;
  }

  .nav-container {
    padding: 0 16px;
  }

  .nav-brand {
    font-size: 1.3rem;
  }

  .nav-brand mat-icon {
    font-size: 24px;
    width: 24px;
    height: 24px;
  }
}

@media (max-width: 480px) {
  .nav-container {
    padding: 0 12px;
  }

  .nav-brand {
    font-size: 1.2rem;
  }

  .mobile-menu {
    padding: 16px;
  }
}

/* Dark theme support */
.dark-theme .unified-navigation {
  background: rgba(30, 30, 30, 0.95);
  border-bottom-color: var(--theme-accent-dark);
}

.dark-theme .mobile-menu {
  background: rgba(30, 30, 30, 0.98);
  border-bottom-color: var(--theme-accent-dark);
}

/* Animation for smooth transitions */
.nav-link,
.mobile-nav-link,
.login-btn,
.register-btn {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Focus states for accessibility */
.nav-link:focus,
.mobile-nav-link:focus,
.login-btn:focus,
.register-btn:focus {
  outline: 2px solid var(--theme-accent);
  outline-offset: 2px;
}

/* Ensure proper spacing for content below navigation */
body {
  padding-top: 64px;
}
