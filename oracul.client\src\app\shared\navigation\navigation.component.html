<!-- Unified Navigation Menu -->
<nav class="unified-navigation">
  <div class="nav-container">
    <!-- Brand -->
    <div class="nav-brand" (click)="navigateToSection('hero')">
      <mat-icon>auto_awesome</mat-icon>
      <span>{{ t.nav.brand }}</span>
    </div>

    <!-- Navigation Links -->
    <div class="nav-links">
      <button mat-button (click)="navigateToSection('hero')"
              class="nav-link"
              [class.active]="isActiveSection('hero')">
        <mat-icon>home</mat-icon>
        {{ t.home.nav.home }}
      </button>
      <button mat-button (click)="navigateToSection('astrologers')"
              class="nav-link"
              [class.active]="isActiveSection('astrologers')">
        <mat-icon>people</mat-icon>
        {{ t.home.nav.astrologers }}
      </button>
      <button mat-button (click)="navigateToSection('articles')"
              class="nav-link"
              [class.active]="isActiveSection('articles')">
        <mat-icon>article</mat-icon>
        {{ t.home.nav.articles }}
      </button>
      <button mat-button (click)="navigateToSection('horoscope')"
              class="nav-link"
              [class.active]="isActiveSection('horoscope')">
        <mat-icon>stars</mat-icon>
        {{ t.home.nav.horoscope }}
      </button>
    </div>

    <!-- Authentication Actions for Anonymous Users -->
    <div class="nav-actions" *ngIf="!isAuthenticated()">
      <button mat-stroked-button color="primary" (click)="navigateToLogin()" class="login-btn">
        <mat-icon>login</mat-icon>
        {{ t.common.login }}
      </button>
      <button mat-raised-button color="primary" (click)="navigateToRegister()" class="register-btn">
        <mat-icon>star</mat-icon>
        {{ t.home.startJourney }}
      </button>
    </div>

    <!-- User Menu for Authenticated Users -->
    <div class="nav-user-menu" *ngIf="isAuthenticated()">
      <!-- User Info -->
      <span class="user-info" *ngIf="authService.currentUser$ | async as user">
        {{ t.dashboard.welcome }}, {{ user.firstName }}!
      </span>

      <button mat-icon-button [matMenuTriggerFor]="userMenu">
        <mat-icon>account_circle</mat-icon>
      </button>
      <mat-menu #userMenu="matMenu" class="user-menu">
        <button mat-menu-item routerLink="/profile/edit">
          <mat-icon>person</mat-icon>
          <span>{{ t.nav.myProfile }}</span>
        </button>
        <button mat-menu-item routerLink="/profiles/search">
          <mat-icon>search</mat-icon>
          <span>{{ t.nav.findProfessionals }}</span>
        </button>
        <button mat-menu-item routerLink="/dashboard">
          <mat-icon>dashboard</mat-icon>
          <span>{{ t.nav.dashboard }}</span>
        </button>
        <button mat-menu-item routerLink="/test-api">
          <mat-icon>api</mat-icon>
          <span>Test API Connection</span>
        </button>
        <button mat-menu-item [matMenuTriggerFor]="themeMenu">
          <mat-icon>palette</mat-icon>
          <span>{{ t.nav.themes }}</span>
          <mat-icon class="submenu-arrow">chevron_right</mat-icon>
        </button>
        <button mat-menu-item>
          <mat-icon>settings</mat-icon>
          <span>{{ t.common.settings }}</span>
        </button>
        <mat-divider></mat-divider>
        <button mat-menu-item (click)="logout()">
          <mat-icon>logout</mat-icon>
          <span>{{ t.nav.logout }}</span>
        </button>
      </mat-menu>

      <!-- Theme submenu -->
      <mat-menu #themeMenu="matMenu" class="theme-menu">
        <div class="theme-menu-content" (click)="$event.stopPropagation()">
          <app-theme-selector></app-theme-selector>
        </div>
      </mat-menu>
    </div>

    <!-- Mobile Menu Toggle -->
    <button mat-icon-button class="mobile-menu-toggle" (click)="toggleMobileMenu()" [class.active]="isMobileMenuOpen">
      <mat-icon>{{ isMobileMenuOpen ? 'close' : 'menu' }}</mat-icon>
    </button>
  </div>

  <!-- Mobile Menu -->
  <div class="mobile-menu" [class.open]="isMobileMenuOpen">
    <button mat-button (click)="navigateToSection('hero')"
            class="mobile-nav-link"
            [class.active]="isActiveSection('hero')">
      <mat-icon>home</mat-icon>
      {{ t.home.nav.home }}
    </button>
    <button mat-button (click)="navigateToSection('astrologers')"
            class="mobile-nav-link"
            [class.active]="isActiveSection('astrologers')">
      <mat-icon>people</mat-icon>
      {{ t.home.nav.astrologers }}
    </button>
    <button mat-button (click)="navigateToSection('articles')"
            class="mobile-nav-link"
            [class.active]="isActiveSection('articles')">
      <mat-icon>article</mat-icon>
      {{ t.home.nav.articles }}
    </button>
    <button mat-button (click)="navigateToSection('horoscope')"
            class="mobile-nav-link"
            [class.active]="isActiveSection('horoscope')">
      <mat-icon>stars</mat-icon>
      {{ t.home.nav.horoscope }}
    </button>

    <!-- Mobile Authentication Actions for Anonymous Users -->
    <div class="mobile-actions" *ngIf="!isAuthenticated()">
      <button mat-stroked-button color="primary" (click)="navigateToLogin()" class="mobile-login-btn">
        <mat-icon>login</mat-icon>
        {{ t.common.login }}
      </button>
      <button mat-raised-button color="primary" (click)="navigateToRegister()" class="mobile-register-btn">
        <mat-icon>star</mat-icon>
        {{ t.home.startJourney }}
      </button>
    </div>

    <!-- Mobile User Actions for Authenticated Users -->
    <div class="mobile-user-actions" *ngIf="isAuthenticated()">
      <button mat-button routerLink="/profile/edit" class="mobile-nav-link">
        <mat-icon>person</mat-icon>
        {{ t.nav.myProfile }}
      </button>
      <button mat-button routerLink="/profiles/search" class="mobile-nav-link">
        <mat-icon>search</mat-icon>
        {{ t.nav.findProfessionals }}
      </button>
      <button mat-button routerLink="/dashboard" class="mobile-nav-link">
        <mat-icon>dashboard</mat-icon>
        {{ t.nav.dashboard }}
      </button>
      <button mat-button (click)="logout()" class="mobile-nav-link">
        <mat-icon>logout</mat-icon>
        {{ t.nav.logout }}
      </button>
    </div>
  </div>
</nav>
