export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber?: string;
  password: string;
  confirmPassword: string;
  acceptTerms: boolean;
}

export interface OracleRegisterRequest extends RegisterRequest {
  // Professional Information
  professionalTitle: string;
  headline: string;
  summary: string;

  // Location Information
  city: string;
  state?: string;
  country: string;
  displayLocation?: string;

  // Contact & Business Information
  website?: string;
  portfolioUrl?: string;
  businessAddress?: {
    street?: string;
    city?: string;
    state?: string;
    postalCode?: string;
    country?: string;
    isPublic: boolean;
  };

  // Oracle-Specific Information
  birthDate: string; // ISO date string
  birthTime?: string; // HH:mm format
  birthLocation: string;
  astrologicalSign?: string;
  primarySpecialization: string;
  yearsOfExperience: number;
  consultationRates?: {
    hourlyRate?: number;
    sessionRate?: number;
    currency: string;
  };
  languagesSpoken: string[];
  oracleTypes: string[]; // Tarot, Astrology, Crystal, etc.

  // Skills
  skills: string[];
}

export interface OAuthLoginRequest {
  provider: 'google' | 'facebook';
  accessToken: string;
  email?: string;
  firstName?: string;
  lastName?: string;
  profilePictureUrl?: string;
}

export interface AuthResponse {
  success: boolean;
  message: string;
  accessToken?: string;
  refreshToken?: string;
  user?: UserInfo;
}

export interface UserInfo {
  id: number;
  email: string;
  firstName: string;
  lastName: string;
  phoneNumber?: string;
  profilePictureUrl?: string;
  emailConfirmed: boolean;
  lastLoginAt?: Date;
  roles: string[];
  permissions: string[];
}

export interface ApiResponse<T> {
  success: boolean;
  message: string;
  data?: T;
  errors: string[];
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

export interface ForgotPasswordRequest {
  email: string;
}

export interface ResetPasswordRequest {
  token: string;
  newPassword: string;
  confirmPassword: string;
}
