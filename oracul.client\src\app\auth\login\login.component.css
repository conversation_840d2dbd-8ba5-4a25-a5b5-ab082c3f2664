.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 20px;
  background: var(--theme-gradient-auth);
}

.login-card {
  width: 100%;
  max-width: 400px;
  padding: 0;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border-radius: 16px;
  overflow: hidden;
}

.login-header {
  background: var(--theme-gradient-auth);
  color: white;
  padding: 24px;
  text-align: center;
}

.login-header .mat-card-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 24px;
  font-weight: 500;
}

.login-icon {
  font-size: 28px;
  width: 28px;
  height: 28px;
}

.login-header .mat-card-subtitle {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  margin: 0;
}

.login-form {
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.full-width {
  width: 100%;
}

.login-button {
  height: 48px;
  font-size: 16px;
  font-weight: 500;
  margin-top: 8px;
}

.login-button .mat-icon {
  margin-right: 8px;
}

.login-actions {
  padding: 0 24px 24px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.action-links {
  display: flex;
  justify-content: center;
}

.register-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.register-text {
  margin: 0;
  color: rgba(0, 0, 0, 0.6);
  font-size: 14px;
}

.mat-form-field {
  margin-bottom: 8px;
}

.mat-form-field-appearance-outline .mat-form-field-outline {
  border-radius: 8px;
}

/* Loading spinner styling */
.mat-spinner {
  margin-right: 8px;
}

/* OAuth Buttons Styling */
.divider-container {
  display: flex;
  align-items: center;
  margin: 24px 0 16px;
  gap: 16px;
}

.divider-text {
  color: rgba(0, 0, 0, 0.6);
  font-size: 14px;
  white-space: nowrap;
}

.oauth-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.oauth-button {
  height: 48px;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.oauth-icon {
  width: 20px;
  height: 20px;
}

.google-button {
  border-color: #dadce0;
  color: #3c4043;
  background-color: #fff;
}

.google-button:hover:not([disabled]) {
  background-color: #f8f9fa;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.facebook-button {
  border-color: #1877f2;
  color: #1877f2;
  background-color: #fff;
}

.facebook-button:hover:not([disabled]) {
  background-color: #f0f2f5;
}

.facebook-icon {
  color: #1877f2;
  font-size: 20px;
}

/* Responsive design */
@media (max-width: 480px) {
  .login-container {
    padding: 16px;
  }

  .login-card {
    max-width: 100%;
  }

  .login-form {
    padding: 20px;
  }

  .login-actions {
    padding: 0 20px 20px;
  }
}

/* Custom snackbar styles */
::ng-deep .success-snackbar {
  background-color: #4caf50;
  color: white;
}

::ng-deep .error-snackbar {
  background-color: #f44336;
  color: white;
}
