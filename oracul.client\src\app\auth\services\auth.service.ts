import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { BehaviorSubject, Observable, throwError } from 'rxjs';
import { map, catchError, tap } from 'rxjs/operators';
import { Router } from '@angular/router';
import {
  LoginRequest,
  RegisterRequest,
  OracleRegisterRequest,
  AuthResponse,
  UserInfo,
  ChangePasswordRequest,
  ForgotPasswordRequest,
  ResetPasswordRequest,
  ApiResponse,
  OAuthLoginRequest
} from '../models/auth.models';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private readonly API_URL = '/api/auth';
  private readonly TOKEN_KEY = 'access_token';
  private readonly REFRESH_TOKEN_KEY = 'refresh_token';
  private readonly USER_KEY = 'user_info';
  private readonly REMEMBER_ME_KEY = 'remember_me';

  private currentUserSubject = new BehaviorSubject<UserInfo | null>(null);
  public currentUser$ = this.currentUserSubject.asObservable();

  private isAuthenticatedSubject = new BehaviorSubject<boolean>(false);
  public isAuthenticated$ = this.isAuthenticatedSubject.asObservable();

  constructor(
    private http: HttpClient,
    private router: Router
  ) {
    this.initializeAuth();
  }

  private initializeAuth(): void {
    const token = this.getToken();
    const user = this.getStoredUser();

    if (token && user) {
      // Check if token is expired
      if (this.isTokenExpired(token)) {
        // Try to refresh the token
        this.refreshToken().subscribe({
          next: () => {
            this.currentUserSubject.next(user);
            this.isAuthenticatedSubject.next(true);
            this.startTokenExpirationCheck();
          },
          error: () => {
            // Refresh failed, clear auth data
            this.clearAuthData();
          }
        });
      } else {
        this.currentUserSubject.next(user);
        this.isAuthenticatedSubject.next(true);
        this.startTokenExpirationCheck();
      }
    }
  }

  login(credentials: LoginRequest): Observable<AuthResponse> {
    return this.http.post<AuthResponse>(`${this.API_URL}/login`, credentials)
      .pipe(
        tap(response => {
          if (response.success && response.accessToken && response.user) {
            // Store remember me preference
            const rememberMe = credentials.rememberMe || false;
            this.setRememberMe(rememberMe);

            this.setToken(response.accessToken, rememberMe);
            if (response.refreshToken) {
              this.setRefreshToken(response.refreshToken, rememberMe);
            }
            this.setUser(response.user, rememberMe);
            this.currentUserSubject.next(response.user);
            this.isAuthenticatedSubject.next(true);
          }
        }),
        catchError(this.handleError)
      );
  }

  register(userData: RegisterRequest | OracleRegisterRequest): Observable<AuthResponse> {
    // Determine endpoint based on data type
    const endpoint = this.isOracleRegistration(userData) ? 'register-oracle' : 'register';

    return this.http.post<AuthResponse>(`${this.API_URL}/${endpoint}`, userData)
      .pipe(
        tap(response => {
          if (response.success && response.accessToken && response.user) {
            this.setToken(response.accessToken);
            if (response.refreshToken) {
              this.setRefreshToken(response.refreshToken);
            }
            this.setUser(response.user);
            this.currentUserSubject.next(response.user);
            this.isAuthenticatedSubject.next(true);
          }
        }),
        catchError(this.handleError)
      );
  }

  private isOracleRegistration(userData: RegisterRequest | OracleRegisterRequest): userData is OracleRegisterRequest {
    return 'professionalTitle' in userData;
  }

  logout(): Observable<any> {
    return this.http.post(`${this.API_URL}/logout`, {})
      .pipe(
        tap(() => {
          this.clearAuthData();
        }),
        catchError(() => {
          // Even if logout fails on server, clear local data
          this.clearAuthData();
          return throwError('Logout failed');
        })
      );
  }

  refreshToken(): Observable<AuthResponse> {
    const refreshToken = this.getRefreshToken();
    if (!refreshToken) {
      return throwError('No refresh token available');
    }

    return this.http.post<AuthResponse>(`${this.API_URL}/refresh-token`, { refreshToken })
      .pipe(
        tap(response => {
          if (response.success && response.accessToken) {
            const rememberMe = this.getRememberMe();
            this.setToken(response.accessToken, rememberMe);
            if (response.refreshToken) {
              this.setRefreshToken(response.refreshToken, rememberMe);
            }
            if (response.user) {
              this.setUser(response.user, rememberMe);
              this.currentUserSubject.next(response.user);
            }
          }
        }),
        catchError(error => {
          this.clearAuthData();
          return throwError(error);
        })
      );
  }

  getCurrentUser(): Observable<UserInfo> {
    return this.http.get<UserInfo>(`${this.API_URL}/me`)
      .pipe(
        tap(user => {
          const rememberMe = this.getRememberMe();
          this.setUser(user, rememberMe);
          this.currentUserSubject.next(user);
        }),
        catchError(this.handleError)
      );
  }

  changePassword(request: ChangePasswordRequest): Observable<ApiResponse<any>> {
    return this.http.post<ApiResponse<any>>(`${this.API_URL}/change-password`, request)
      .pipe(catchError(this.handleError));
  }

  forgotPassword(request: ForgotPasswordRequest): Observable<ApiResponse<any>> {
    return this.http.post<ApiResponse<any>>(`${this.API_URL}/forgot-password`, request)
      .pipe(catchError(this.handleError));
  }

  resetPassword(request: ResetPasswordRequest): Observable<ApiResponse<any>> {
    return this.http.post<ApiResponse<any>>(`${this.API_URL}/reset-password`, request)
      .pipe(catchError(this.handleError));
  }

  loginWithOAuth(request: OAuthLoginRequest): Observable<AuthResponse> {
    return this.http.post<AuthResponse>(`${this.API_URL}/oauth-login`, request)
      .pipe(
        tap(response => {
          if (response.success && response.accessToken && response.user) {
            // For OAuth, default to remember me = true for better UX
            const rememberMe = true;
            this.setRememberMe(rememberMe);

            this.setToken(response.accessToken, rememberMe);
            if (response.refreshToken) {
              this.setRefreshToken(response.refreshToken, rememberMe);
            }
            this.setUser(response.user, rememberMe);
            this.currentUserSubject.next(response.user);
            this.isAuthenticatedSubject.next(true);
          }
        }),
        catchError(this.handleError)
      );
  }

  // Token management
  getToken(): string | null {
    return localStorage.getItem(this.TOKEN_KEY) || sessionStorage.getItem(this.TOKEN_KEY);
  }

  private setToken(token: string, rememberMe: boolean = false): void {
    if (rememberMe) {
      localStorage.setItem(this.TOKEN_KEY, token);
      sessionStorage.removeItem(this.TOKEN_KEY);
    } else {
      sessionStorage.setItem(this.TOKEN_KEY, token);
      localStorage.removeItem(this.TOKEN_KEY);
    }
  }

  private getRefreshToken(): string | null {
    return localStorage.getItem(this.REFRESH_TOKEN_KEY) || sessionStorage.getItem(this.REFRESH_TOKEN_KEY);
  }

  private setRefreshToken(token: string, rememberMe: boolean = false): void {
    if (rememberMe) {
      localStorage.setItem(this.REFRESH_TOKEN_KEY, token);
      sessionStorage.removeItem(this.REFRESH_TOKEN_KEY);
    } else {
      sessionStorage.setItem(this.REFRESH_TOKEN_KEY, token);
      localStorage.removeItem(this.REFRESH_TOKEN_KEY);
    }
  }

  private setUser(user: UserInfo, rememberMe: boolean = false): void {
    const userStr = JSON.stringify(user);
    if (rememberMe) {
      localStorage.setItem(this.USER_KEY, userStr);
      sessionStorage.removeItem(this.USER_KEY);
    } else {
      sessionStorage.setItem(this.USER_KEY, userStr);
      localStorage.removeItem(this.USER_KEY);
    }
  }

  private getStoredUser(): UserInfo | null {
    const userStr = localStorage.getItem(this.USER_KEY) || sessionStorage.getItem(this.USER_KEY);
    return userStr ? JSON.parse(userStr) : null;
  }

  private setRememberMe(rememberMe: boolean): void {
    if (rememberMe) {
      localStorage.setItem(this.REMEMBER_ME_KEY, 'true');
    } else {
      localStorage.removeItem(this.REMEMBER_ME_KEY);
    }
  }

  private getRememberMe(): boolean {
    return localStorage.getItem(this.REMEMBER_ME_KEY) === 'true';
  }

  private isTokenExpired(token: string): boolean {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      const currentTime = Math.floor(Date.now() / 1000);
      return payload.exp < currentTime;
    } catch (error) {
      return true; // If we can't parse the token, consider it expired
    }
  }

  checkTokenExpiration(): void {
    const token = this.getToken();
    if (token && this.isTokenExpired(token)) {
      // Try to refresh the token
      this.refreshToken().subscribe({
        next: () => {
          // Token refreshed successfully
        },
        error: () => {
          // Refresh failed, logout user
          this.clearAuthData();
        }
      });
    }
  }

  startTokenExpirationCheck(): void {
    // Check token expiration every 5 minutes
    setInterval(() => {
      this.checkTokenExpiration();
    }, 5 * 60 * 1000);
  }

  private clearAuthData(): void {
    // Clear from both localStorage and sessionStorage
    localStorage.removeItem(this.TOKEN_KEY);
    localStorage.removeItem(this.REFRESH_TOKEN_KEY);
    localStorage.removeItem(this.USER_KEY);
    localStorage.removeItem(this.REMEMBER_ME_KEY);

    sessionStorage.removeItem(this.TOKEN_KEY);
    sessionStorage.removeItem(this.REFRESH_TOKEN_KEY);
    sessionStorage.removeItem(this.USER_KEY);

    this.currentUserSubject.next(null);
    this.isAuthenticatedSubject.next(false);
    this.router.navigate(['/login']);
  }

  isAuthenticated(): boolean {
    const token = this.getToken();
    return !!token && !this.isTokenExpired(token);
  }



  hasRole(role: string): boolean {
    const user = this.currentUserSubject.value;
    return user?.roles.includes(role) || false;
  }

  hasPermission(permission: string): boolean {
    const user = this.currentUserSubject.value;
    return user?.permissions.includes(permission) || false;
  }

  private handleError(error: any): Observable<never> {
    let errorMessage = 'An error occurred';

    if (error.error?.message) {
      errorMessage = error.error.message;
    } else if (error.message) {
      errorMessage = error.message;
    }

    return throwError(errorMessage);
  }
}
